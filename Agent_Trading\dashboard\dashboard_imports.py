"""
Dashboard Import Wrapper
========================

Safe import wrapper for dashboard components to handle import path issues.
This module provides a clean interface for the dashboard to access trading engine components.
"""

import sys
from pathlib import Path

# Setup paths for dashboard context
dashboard_dir = Path(__file__).parent.absolute()
agent_trading_dir = dashboard_dir.parent.absolute()

# Add necessary paths
sys.path.insert(0, str(agent_trading_dir))

def safe_import_trading_components():
    """Safely import trading engine components for dashboard use"""
    
    try:
        # Import core components with fallback
        try:
            from core.config_manager import ConfigManager
            from crypto_market.engines.data_engine.database_manager import DatabaseManager
        except ImportError as core_error:
            print(f"⚠️ Core module import failed: {core_error}")
            print("🔄 Creating fallback components...")

            # Create fallback classes
            class ConfigManager:
                def __init__(self, config_path):
                    self.config_path = config_path
                    print(f"📝 Fallback ConfigManager initialized")

            class DatabaseManager:
                def __init__(self, config):
                    self.config = config
                    print(f"🗄️ Fallback DatabaseManager initialized")

                def get_market_data(self, timeframe='3m', start_date=None, end_date=None, limit=None):
                    import pandas as pd
                    import numpy as np
                    from datetime import datetime, timedelta

                    # Generate sample data for fallback
                    if limit:
                        periods = limit
                    else:
                        periods = 1000

                    dates = pd.date_range(start=datetime.now() - timedelta(days=periods//100), periods=periods, freq='3min')

                    # Generate realistic BTC-like price data
                    np.random.seed(42)
                    base_price = 50000
                    price_changes = np.random.normal(0, 100, periods)
                    prices = base_price + np.cumsum(price_changes)

                    df = pd.DataFrame({
                        'open': prices + np.random.normal(0, 50, periods),
                        'high': prices + np.abs(np.random.normal(100, 50, periods)),
                        'low': prices - np.abs(np.random.normal(100, 50, periods)),
                        'close': prices,
                        'volume': np.random.uniform(100, 1000, periods)
                    }, index=dates)

                    print(f"📊 Generated {len(df)} sample candles for {timeframe}")
                    return df
        
        # Try to import VectorBT backtester, fallback to simple backtester if VectorBT has issues
        try:
            from crypto_market.engines.Trading_engine.backtester_vbt import BigTrendVectorBTBacktester
        except ImportError as vbt_error:
            print(f"⚠️ VectorBT import failed: {vbt_error}")
            print("🔄 Using fallback simple backtester...")

            # Create a simple fallback backtester class
            class BigTrendVectorBTBacktester:
                """Fallback backtester when VectorBT is not available"""

                def __init__(self, initial_capital=100000):
                    self.initial_capital = initial_capital

                    # Initialize strategy settings that the dashboard expects
                    self.strategy_settings = {
                        'confluence_threshold': 3,
                        'min_rr_ratio': 2.0,
                        'max_risk_per_trade': 0.02,
                        'fees': 0.001,
                        'slippage': 0.0005,
                        'start_trailing_at_rr': 4.0,
                        'trailing_step': 50,
                        'min_time_between_trades': 30
                    }

                    # Initialize other expected attributes
                    self.results = {}
                    self.visualization_data = {}

                    print(f"📊 Initialized fallback backtester with ${initial_capital:,}")
                    print("⚠️ VectorBT not available - using fallback mode")

                def run_full_backtest(self, df_3m, df_15m, df_1h):
                    """Simple fallback backtest implementation"""
                    print("🚀 Running fallback backtest (VectorBT not available)")

                    # Simulate some processing time
                    import time
                    time.sleep(1)

                    # Generate more realistic mock results based on data length
                    data_length = len(df_3m) if df_3m is not None else 1000
                    estimated_trades = max(5, data_length // 100)  # Roughly 1 trade per 100 candles

                    # Simple mock results for demonstration
                    results = {
                        'total_return': 0.15,  # 15% return
                        'total_trades': estimated_trades,
                        'win_rate': 0.60,  # 60% win rate
                        'big_winners': max(2, estimated_trades // 4),
                        'max_drawdown': 0.08,  # 8% max drawdown
                        'sharpe_ratio': 1.2,
                        'profit_factor': 1.8,
                        'final_value': self.initial_capital * 1.15,
                        'avg_trade_duration': '4.2 hours',
                        'best_trade': self.initial_capital * 0.08,  # 8% gain
                        'worst_trade': -self.initial_capital * 0.03,  # 3% loss
                        'consecutive_wins': 5,
                        'consecutive_losses': 2,
                        'status': 'fallback_mode',
                        'message': 'VectorBT not available - using fallback results',
                        'portfolio': None  # No actual portfolio object
                    }

                    # Store results
                    self.results = results

                    # Create mock visualization data
                    self._create_mock_visualization_data(df_3m)

                    print(f"✅ Fallback backtest completed:")
                    print(f"   Total Return: {results['total_return']:.1%}")
                    print(f"   Total Trades: {results['total_trades']}")
                    print(f"   Win Rate: {results['win_rate']:.1%}")
                    print(f"   Big Winners: {results['big_winners']}")
                    print(f"   ⚠️ Note: These are fallback results. Install VectorBT for real backtesting.")

                    return results

                def _create_mock_visualization_data(self, df_3m):
                    """Create mock visualization data for dashboard"""
                    import pandas as pd
                    import numpy as np

                    if df_3m is not None and len(df_3m) > 0:
                        # Create mock portfolio value series
                        portfolio_values = []
                        initial_value = self.initial_capital

                        for i in range(len(df_3m)):
                            # Simulate portfolio growth with some volatility
                            growth_factor = 1 + np.random.normal(0.0001, 0.002)  # Small daily changes
                            initial_value *= growth_factor
                            portfolio_values.append(initial_value)

                        # Create mock trades
                        num_trades = self.results.get('total_trades', 10)
                        trade_indices = np.random.choice(len(df_3m), min(num_trades, len(df_3m)//10), replace=False)

                        mock_trades = pd.DataFrame({
                            'Entry Time': [df_3m.index[i] for i in trade_indices],
                            'Exit Time': [df_3m.index[min(i+20, len(df_3m)-1)] for i in trade_indices],
                            'Entry Price': [df_3m.iloc[i]['close'] for i in trade_indices],
                            'Exit Price': [df_3m.iloc[min(i+20, len(df_3m)-1)]['close'] * np.random.uniform(0.98, 1.05) for i in trade_indices],
                            'PnL': [np.random.uniform(-500, 1200) for _ in trade_indices],
                            'Size': [np.random.uniform(0.1, 1.0) for _ in trade_indices]
                        })

                        self.visualization_data = {
                            'price_data': df_3m[['open', 'high', 'low', 'close', 'volume']].copy(),
                            'portfolio_value': pd.Series(portfolio_values, index=df_3m.index),
                            'trades': mock_trades,
                            'signals': pd.DataFrame(),  # Empty signals for fallback
                            'drawdown': pd.Series(np.random.uniform(-0.1, 0, len(df_3m)), index=df_3m.index),
                            'returns': pd.Series(np.random.normal(0.0001, 0.01, len(df_3m)), index=df_3m.index),
                            'entry_points': trade_indices,
                            'exit_points': [min(i+20, len(df_3m)-1) for i in trade_indices],
                            'entry_prices': [df_3m.iloc[i]['close'] for i in trade_indices],
                            'exit_prices': [df_3m.iloc[min(i+20, len(df_3m)-1)]['close'] for i in trade_indices]
                        }
                    else:
                        self.visualization_data = {}
        
        # Create a simplified data loader for dashboard use
        class DashboardDataLoader:
            """Simplified data loader for dashboard use"""
            
            def __init__(self):
                """Initialize with dashboard-safe configuration"""
                config_path = str(agent_trading_dir / "config.json")
                self.config = ConfigManager(config_path)
                self.db_manager = DatabaseManager(self.config)
            
            def load_recent_data(self, days=30):
                """Load recent data for backtesting"""
                from datetime import datetime, timedelta
                
                end_date = datetime.now()
                start_date = end_date - timedelta(days=days)
                
                # Load data for each timeframe
                df_3m = self.db_manager.get_market_data(
                    timeframe='3m',
                    start_date=start_date,
                    end_date=end_date
                )
                
                df_15m = self.db_manager.get_market_data(
                    timeframe='15m',
                    start_date=start_date,
                    end_date=end_date
                )
                
                df_1h = self.db_manager.get_market_data(
                    timeframe='1h',
                    start_date=start_date,
                    end_date=end_date
                )
                
                return df_3m, df_15m, df_1h
            
            def load_backtest_data(self, start_date=None, end_date=None):
                """Load backtest data with date range"""
                
                # Load data for each timeframe
                df_3m = self.db_manager.get_market_data(
                    timeframe='3m',
                    start_date=start_date,
                    end_date=end_date
                )
                
                df_15m = self.db_manager.get_market_data(
                    timeframe='15m',
                    start_date=start_date,
                    end_date=end_date
                )
                
                df_1h = self.db_manager.get_market_data(
                    timeframe='1h',
                    start_date=start_date,
                    end_date=end_date
                )
                
                return df_3m, df_15m, df_1h
            
            def load_sample_data(self, sample_size=5000):
                """Load sample data for testing"""
                
                # Load recent data and sample it
                df_3m = self.db_manager.get_market_data(timeframe='3m', limit=sample_size)
                
                # Calculate corresponding samples for other timeframes
                sample_15m = max(1, sample_size // 5)  # 3m to 15m ratio
                sample_1h = max(1, sample_size // 20)  # 3m to 1h ratio
                
                df_15m = self.db_manager.get_market_data(timeframe='15m', limit=sample_15m)
                df_1h = self.db_manager.get_market_data(timeframe='1h', limit=sample_1h)
                
                return df_3m, df_15m, df_1h
        
        return DashboardDataLoader, BigTrendVectorBTBacktester
        
    except ImportError as e:
        print(f"Import error in dashboard_imports: {e}")
        raise
    except Exception as e:
        print(f"Unexpected error in dashboard_imports: {e}")
        raise

def get_trading_components():
    """Get trading components for dashboard use"""
    return safe_import_trading_components()
