#!/usr/bin/env python3
"""
Test Dashboard Import Fixes
Verify that the dashboard can properly import the trading engine components
"""

import sys
from pathlib import Path

# Add paths
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

def test_dashboard_imports():
    """Test that dashboard imports work correctly"""
    
    print("🧪 TESTING DASHBOARD IMPORT FIXES")
    print("=" * 50)
    
    try:
        print("📦 Testing Dashboard-safe imports...")

        # Test the dashboard-safe imports
        from dashboard.dashboard_imports import get_trading_components
        TradingDataLoader, BigTrendVectorBTBacktester = get_trading_components()
        
        print("✅ Successfully imported TradingDataLoader")
        print("✅ Successfully imported BigTrendVectorBTBacktester")
        
        # Test instantiation
        print("\n🔧 Testing component instantiation...")
        
        loader = TradingDataLoader()
        print("✅ TradingDataLoader instantiated successfully")
        
        backtester = BigTrendVectorBTBacktester(initial_capital=100000)
        print("✅ BigTrendVectorBTBacktester instantiated successfully")
        
        print("\n🎯 Testing dashboard import simulation...")

        # Simulate the dashboard import process
        try:
            # This is what the dashboard does now
            from dashboard.dashboard_imports import get_trading_components
            DashboardLoader, DashboardBacktester = get_trading_components()

            print("✅ Dashboard-style imports successful")

            # Test that they work
            dashboard_loader = DashboardLoader()
            dashboard_backtester = DashboardBacktester(initial_capital=50000)

            print("✅ Dashboard components instantiated successfully")
            
        except Exception as e:
            print(f"❌ Dashboard import simulation failed: {e}")
            return False
        
        print("\n🏆 ALL IMPORT TESTS PASSED!")
        print("=" * 50)
        print("✅ Dashboard imports are now working correctly")
        print("✅ TradingDataLoader can be imported and used")
        print("✅ BigTrendVectorBTBacktester can be imported and used")
        print("✅ Dashboard backtesting functionality should work")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("The import paths may still need adjustment")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dashboard_functionality():
    """Test basic dashboard functionality"""
    
    print("\n🎯 TESTING DASHBOARD FUNCTIONALITY")
    print("=" * 50)
    
    try:
        # Test that we can access the main dashboard components
        print("📊 Testing dashboard app structure...")
        
        # Import the dashboard
        from dashboard.app import SimpleTradingDashboard
        print("✅ Dashboard app imported successfully")
        
        # Note: We won't instantiate it here as it requires Streamlit context
        print("✅ Dashboard class is accessible")
        
        print("\n🎉 DASHBOARD FUNCTIONALITY TEST COMPLETED")
        print("The dashboard should now work without import errors!")
        
        return True
        
    except Exception as e:
        print(f"❌ Dashboard functionality test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 STARTING DASHBOARD IMPORT TESTS")
    print("=" * 60)
    
    # Test 1: Import fixes
    imports_ok = test_dashboard_imports()
    
    # Test 2: Dashboard functionality
    dashboard_ok = test_dashboard_functionality()
    
    print("\n" + "=" * 60)
    if imports_ok and dashboard_ok:
        print("🏆 ALL TESTS PASSED!")
        print("✅ Dashboard import issues have been resolved")
        print("✅ You can now run the dashboard with:")
        print("   cd Agent_Trading")
        print("   streamlit run dashboard/app.py")
        print("✅ Backtesting functionality should work correctly")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please check the error messages above")
        
    print("=" * 60)
