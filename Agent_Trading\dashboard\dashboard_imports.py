"""
Dashboard Import Wrapper
========================

Safe import wrapper for dashboard components to handle import path issues.
This module provides a clean interface for the dashboard to access trading engine components.
"""

import sys
from pathlib import Path

# Setup paths for dashboard context
dashboard_dir = Path(__file__).parent.absolute()
agent_trading_dir = dashboard_dir.parent.absolute()

# Add necessary paths
sys.path.insert(0, str(agent_trading_dir))

def safe_import_trading_components():
    """Safely import trading engine components for dashboard use"""
    
    try:
        # Import core components
        from core.config_manager import ConfigManager
        from crypto_market.engines.data_engine.database_manager import DatabaseManager
        
        # Import trading engine components
        from crypto_market.engines.Trading_engine.backtester_vbt import BigTrendVectorBTBacktester
        
        # Create a simplified data loader for dashboard use
        class DashboardDataLoader:
            """Simplified data loader for dashboard use"""
            
            def __init__(self):
                """Initialize with dashboard-safe configuration"""
                config_path = str(agent_trading_dir / "config.json")
                self.config = ConfigManager(config_path)
                self.db_manager = DatabaseManager(self.config)
            
            def load_recent_data(self, days=30):
                """Load recent data for backtesting"""
                from datetime import datetime, timedelta
                
                end_date = datetime.now()
                start_date = end_date - timedelta(days=days)
                
                # Load data for each timeframe
                df_3m = self.db_manager.get_market_data(
                    timeframe='3m',
                    start_date=start_date,
                    end_date=end_date
                )
                
                df_15m = self.db_manager.get_market_data(
                    timeframe='15m',
                    start_date=start_date,
                    end_date=end_date
                )
                
                df_1h = self.db_manager.get_market_data(
                    timeframe='1h',
                    start_date=start_date,
                    end_date=end_date
                )
                
                return df_3m, df_15m, df_1h
            
            def load_backtest_data(self, start_date=None, end_date=None):
                """Load backtest data with date range"""
                
                # Load data for each timeframe
                df_3m = self.db_manager.get_market_data(
                    timeframe='3m',
                    start_date=start_date,
                    end_date=end_date
                )
                
                df_15m = self.db_manager.get_market_data(
                    timeframe='15m',
                    start_date=start_date,
                    end_date=end_date
                )
                
                df_1h = self.db_manager.get_market_data(
                    timeframe='1h',
                    start_date=start_date,
                    end_date=end_date
                )
                
                return df_3m, df_15m, df_1h
            
            def load_sample_data(self, sample_size=5000):
                """Load sample data for testing"""
                
                # Load recent data and sample it
                df_3m = self.db_manager.get_market_data(timeframe='3m', limit=sample_size)
                
                # Calculate corresponding samples for other timeframes
                sample_15m = max(1, sample_size // 5)  # 3m to 15m ratio
                sample_1h = max(1, sample_size // 20)  # 3m to 1h ratio
                
                df_15m = self.db_manager.get_market_data(timeframe='15m', limit=sample_15m)
                df_1h = self.db_manager.get_market_data(timeframe='1h', limit=sample_1h)
                
                return df_3m, df_15m, df_1h
        
        return DashboardDataLoader, BigTrendVectorBTBacktester
        
    except ImportError as e:
        print(f"Import error in dashboard_imports: {e}")
        raise
    except Exception as e:
        print(f"Unexpected error in dashboard_imports: {e}")
        raise

def get_trading_components():
    """Get trading components for dashboard use"""
    return safe_import_trading_components()
